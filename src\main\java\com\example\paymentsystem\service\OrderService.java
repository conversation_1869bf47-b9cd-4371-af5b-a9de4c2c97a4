package com.example.paymentsystem.service;

import com.example.paymentsystem.dto.PaymentRequest;
import com.example.paymentsystem.entity.Order;
import com.example.paymentsystem.entity.PaymentRecord;
import com.example.paymentsystem.enums.OrderStatus;
import com.example.paymentsystem.enums.PaymentStatus;
import com.example.paymentsystem.repository.OrderRepository;
import com.example.paymentsystem.repository.PaymentRecordRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

/**
 * 订单服务
 */
@Service
public class OrderService {
    
    private static final Logger logger = LoggerFactory.getLogger(OrderService.class);
    
    @Autowired
    private OrderRepository orderRepository;
    
    @Autowired
    private PaymentRecordRepository paymentRecordRepository;
    
    /**
     * 创建订单
     */
    @Transactional
    public Order createOrder(PaymentRequest request) {
        // 生成订单ID
        String orderId = generateOrderId();
        
        // 创建订单
        Order order = new Order();
        order.setOrderId(orderId);
        order.setUserId(request.getUserId());
        order.setAmount(request.getAmount());
        order.setCurrency(request.getCurrency());
        order.setStatus(OrderStatus.PENDING.getCode());
        order.setPaymentMethod(request.getPaymentMethod());
        order.setSubject(request.getSubject());
        order.setDescription(request.getDescription());
        
        // 保存订单
        order = orderRepository.save(order);
        
        // 创建支付记录
        PaymentRecord paymentRecord = new PaymentRecord();
        paymentRecord.setOrderId(orderId);
        paymentRecord.setPaymentMethod(request.getPaymentMethod());
        paymentRecord.setAmount(request.getAmount());
        paymentRecord.setStatus(PaymentStatus.PENDING.getCode());
        
        paymentRecordRepository.save(paymentRecord);
        
        logger.info("订单创建成功，订单号：{}，用户ID：{}，金额：{}", orderId, request.getUserId(), request.getAmount());
        
        return order;
    }
    
    /**
     * 根据订单ID查找订单
     */
    public Optional<Order> findByOrderId(String orderId) {
        return orderRepository.findByOrderId(orderId);
    }
    
    /**
     * 根据用户ID查找订单列表
     */
    public List<Order> findByUserId(Long userId) {
        return orderRepository.findByUserId(userId);
    }
    
    /**
     * 更新订单状态
     */
    @Transactional
    public void updateOrderStatus(String orderId, OrderStatus status) {
        Optional<Order> orderOpt = orderRepository.findByOrderId(orderId);
        if (orderOpt.isPresent()) {
            Order order = orderOpt.get();
            order.setStatus(status.getCode());
            orderRepository.save(order);
            
            logger.info("订单状态更新成功，订单号：{}，状态：{}", orderId, status.getCode());
        } else {
            logger.warn("订单不存在，无法更新状态，订单号：{}", orderId);
        }
    }
    
    /**
     * 更新支付记录状态
     */
    @Transactional
    public void updatePaymentRecordStatus(String orderId, String paymentMethod, PaymentStatus status, String transactionId) {
        Optional<PaymentRecord> recordOpt = paymentRecordRepository.findByOrderIdAndPaymentMethod(orderId, paymentMethod);
        if (recordOpt.isPresent()) {
            PaymentRecord record = recordOpt.get();
            record.setStatus(status.getCode());
            if (transactionId != null) {
                record.setTransactionId(transactionId);
            }
            paymentRecordRepository.save(record);
            
            logger.info("支付记录状态更新成功，订单号：{}，状态：{}", orderId, status.getCode());
        } else {
            logger.warn("支付记录不存在，无法更新状态，订单号：{}，支付方式：{}", orderId, paymentMethod);
        }
    }
    
    /**
     * 生成订单ID
     */
    private String generateOrderId() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
        return "ORDER_" + timestamp;
    }
    
    /**
     * 根据状态查找订单
     */
    public List<Order> findByStatus(OrderStatus status) {
        return orderRepository.findByStatus(status.getCode());
    }
    
    /**
     * 查找支付记录
     */
    public List<PaymentRecord> findPaymentRecordsByOrderId(String orderId) {
        return paymentRecordRepository.findByOrderId(orderId);
    }
}
