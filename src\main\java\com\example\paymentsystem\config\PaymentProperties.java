package com.example.paymentsystem.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 支付配置属性类
 */
@Component
@ConfigurationProperties(prefix = "payment")
public class PaymentProperties {
    
    private Alipay alipay = new Alipay();
    private Wechat wechat = new Wechat();
    private Common common = new Common();
    
    public static class Alipay {
        private String appId;
        private String privateKey;
        private String publicKey;
        private String gatewayUrl;
        
        // Getters and Setters
        public String getAppId() {
            return appId;
        }
        
        public void setAppId(String appId) {
            this.appId = appId;
        }
        
        public String getPrivateKey() {
            return privateKey;
        }
        
        public void setPrivateKey(String privateKey) {
            this.privateKey = privateKey;
        }
        
        public String getPublicKey() {
            return publicKey;
        }
        
        public void setPublicKey(String publicKey) {
            this.publicKey = publicKey;
        }
        
        public String getGatewayUrl() {
            return gatewayUrl;
        }
        
        public void setGatewayUrl(String gatewayUrl) {
            this.gatewayUrl = gatewayUrl;
        }
    }
    
    public static class Wechat {
        private String appId;
        private String mchId;
        private String apiKey;
        private String certPath;
        
        // Getters and Setters
        public String getAppId() {
            return appId;
        }
        
        public void setAppId(String appId) {
            this.appId = appId;
        }
        
        public String getMchId() {
            return mchId;
        }
        
        public void setMchId(String mchId) {
            this.mchId = mchId;
        }
        
        public String getApiKey() {
            return apiKey;
        }
        
        public void setApiKey(String apiKey) {
            this.apiKey = apiKey;
        }
        
        public String getCertPath() {
            return certPath;
        }
        
        public void setCertPath(String certPath) {
            this.certPath = certPath;
        }
    }
    
    public static class Common {
        private int timeout;
        private int retryTimes;
        private String notifyUrl;
        
        // Getters and Setters
        public int getTimeout() {
            return timeout;
        }
        
        public void setTimeout(int timeout) {
            this.timeout = timeout;
        }
        
        public int getRetryTimes() {
            return retryTimes;
        }
        
        public void setRetryTimes(int retryTimes) {
            this.retryTimes = retryTimes;
        }
        
        public String getNotifyUrl() {
            return notifyUrl;
        }
        
        public void setNotifyUrl(String notifyUrl) {
            this.notifyUrl = notifyUrl;
        }
    }
    
    // Main class getters and setters
    public Alipay getAlipay() {
        return alipay;
    }
    
    public void setAlipay(Alipay alipay) {
        this.alipay = alipay;
    }
    
    public Wechat getWechat() {
        return wechat;
    }
    
    public void setWechat(Wechat wechat) {
        this.wechat = wechat;
    }
    
    public Common getCommon() {
        return common;
    }
    
    public void setCommon(Common common) {
        this.common = common;
    }
}
