package com.example.paymentsystem.controller;

import com.example.paymentsystem.config.PaymentProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 支付控制器
 */
@RestController
@RequestMapping("/api/payment")
public class PaymentController {
    
    @Autowired
    private PaymentProperties paymentProperties;
    
    /**
     * 获取支付配置信息（用于测试配置是否正确加载）
     */
    @GetMapping("/config")
    public Map<String, Object> getPaymentConfig() {
        Map<String, Object> config = new HashMap<>();
        
        // 支付宝配置（隐藏敏感信息）
        Map<String, Object> alipayConfig = new HashMap<>();
        alipayConfig.put("appId", maskSensitiveInfo(paymentProperties.getAlipay().getAppId()));
        alipayConfig.put("gatewayUrl", paymentProperties.getAlipay().getGatewayUrl());
        alipayConfig.put("privateKeyConfigured", paymentProperties.getAlipay().getPrivateKey() != null && !paymentProperties.getAlipay().getPrivateKey().isEmpty());
        alipayConfig.put("publicKeyConfigured", paymentProperties.getAlipay().getPublicKey() != null && !paymentProperties.getAlipay().getPublicKey().isEmpty());
        
        // 微信配置（隐藏敏感信息）
        Map<String, Object> wechatConfig = new HashMap<>();
        wechatConfig.put("appId", maskSensitiveInfo(paymentProperties.getWechat().getAppId()));
        wechatConfig.put("mchId", maskSensitiveInfo(paymentProperties.getWechat().getMchId()));
        wechatConfig.put("apiKeyConfigured", paymentProperties.getWechat().getApiKey() != null && !paymentProperties.getWechat().getApiKey().isEmpty());
        wechatConfig.put("certPath", paymentProperties.getWechat().getCertPath());
        
        // 通用配置
        Map<String, Object> commonConfig = new HashMap<>();
        commonConfig.put("timeout", paymentProperties.getCommon().getTimeout());
        commonConfig.put("retryTimes", paymentProperties.getCommon().getRetryTimes());
        commonConfig.put("notifyUrl", paymentProperties.getCommon().getNotifyUrl());
        
        config.put("alipay", alipayConfig);
        config.put("wechat", wechatConfig);
        config.put("common", commonConfig);
        
        return config;
    }
    
    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public Map<String, String> health() {
        Map<String, String> result = new HashMap<>();
        result.put("status", "UP");
        result.put("service", "Payment System");
        return result;
    }
    
    /**
     * 隐藏敏感信息
     */
    private String maskSensitiveInfo(String info) {
        if (info == null || info.length() <= 8) {
            return "****";
        }
        return info.substring(0, 4) + "****" + info.substring(info.length() - 4);
    }
}
