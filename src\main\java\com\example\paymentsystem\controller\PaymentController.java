package com.example.paymentsystem.controller;

import com.example.paymentsystem.config.PaymentProperties;
import com.example.paymentsystem.dto.ApiResponse;
import com.example.paymentsystem.dto.PaymentRequest;
import com.example.paymentsystem.dto.PaymentResponse;
import com.example.paymentsystem.entity.Order;
import com.example.paymentsystem.service.OrderService;
import com.example.paymentsystem.service.PaymentGateway;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 支付控制器
 */
@RestController
@RequestMapping("/api/payment")
public class PaymentController {

    @Autowired
    private PaymentProperties paymentProperties;

    @Autowired
    private PaymentGateway paymentGateway;

    @Autowired
    private OrderService orderService;

    /**
     * 创建支付
     */
    @PostMapping("/create")
    public ApiResponse<PaymentResponse> createPayment(@RequestBody PaymentRequest request) {
        try {
            PaymentResponse response = paymentGateway.createPayment(request);

            if ("FAILED".equals(response.getStatus())) {
                return ApiResponse.error(response.getMessage());
            }

            return ApiResponse.success("支付创建成功", response);

        } catch (Exception e) {
            return ApiResponse.error("创建支付失败：" + e.getMessage());
        }
    }

    /**
     * 查询支付状态
     */
    @GetMapping("/query/{orderId}")
    public ApiResponse<PaymentResponse> queryPayment(@PathVariable String orderId,
                                                   @RequestParam String paymentMethod) {
        try {
            PaymentResponse response = paymentGateway.queryPayment(orderId, paymentMethod);
            return ApiResponse.success("查询成功", response);

        } catch (Exception e) {
            return ApiResponse.error("查询支付状态失败：" + e.getMessage());
        }
    }

    /**
     * 查询订单信息
     */
    @GetMapping("/order/{orderId}")
    public ApiResponse<Order> getOrder(@PathVariable String orderId) {
        try {
            Optional<Order> orderOpt = orderService.findByOrderId(orderId);
            if (orderOpt.isPresent()) {
                return ApiResponse.success("查询成功", orderOpt.get());
            } else {
                return ApiResponse.error("订单不存在");
            }

        } catch (Exception e) {
            return ApiResponse.error("查询订单失败：" + e.getMessage());
        }
    }

    /**
     * 查询用户订单列表
     */
    @GetMapping("/orders/{userId}")
    public ApiResponse<List<Order>> getUserOrders(@PathVariable Long userId) {
        try {
            List<Order> orders = orderService.findByUserId(userId);
            return ApiResponse.success("查询成功", orders);

        } catch (Exception e) {
            return ApiResponse.error("查询用户订单失败：" + e.getMessage());
        }
    }

    /**
     * 支付回调处理（支付宝）
     */
    @PostMapping("/notify/alipay")
    public String handleAlipayCallback(@RequestBody String callbackData) {
        try {
            boolean success = paymentGateway.handleCallback("ALIPAY", callbackData);
            return success ? "success" : "fail";

        } catch (Exception e) {
            return "fail";
        }
    }

    /**
     * 获取支付配置信息（用于测试配置是否正确加载）
     */
    @GetMapping("/config")
    public Map<String, Object> getPaymentConfig() {
        Map<String, Object> config = new HashMap<>();

        // 支付宝配置（隐藏敏感信息）
        Map<String, Object> alipayConfig = new HashMap<>();
        alipayConfig.put("appId", maskSensitiveInfo(paymentProperties.getAlipay().getAppId()));
        alipayConfig.put("gatewayUrl", paymentProperties.getAlipay().getGatewayUrl());
        alipayConfig.put("privateKeyConfigured", paymentProperties.getAlipay().getPrivateKey() != null && !paymentProperties.getAlipay().getPrivateKey().isEmpty());
        alipayConfig.put("publicKeyConfigured", paymentProperties.getAlipay().getPublicKey() != null && !paymentProperties.getAlipay().getPublicKey().isEmpty());

        // 微信配置（隐藏敏感信息）
        Map<String, Object> wechatConfig = new HashMap<>();
        wechatConfig.put("appId", maskSensitiveInfo(paymentProperties.getWechat().getAppId()));
        wechatConfig.put("mchId", maskSensitiveInfo(paymentProperties.getWechat().getMchId()));
        wechatConfig.put("apiKeyConfigured", paymentProperties.getWechat().getApiKey() != null && !paymentProperties.getWechat().getApiKey().isEmpty());
        wechatConfig.put("certPath", paymentProperties.getWechat().getCertPath());

        // 通用配置
        Map<String, Object> commonConfig = new HashMap<>();
        commonConfig.put("timeout", paymentProperties.getCommon().getTimeout());
        commonConfig.put("retryTimes", paymentProperties.getCommon().getRetryTimes());
        commonConfig.put("notifyUrl", paymentProperties.getCommon().getNotifyUrl());

        config.put("alipay", alipayConfig);
        config.put("wechat", wechatConfig);
        config.put("common", commonConfig);

        return config;
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public Map<String, String> health() {
        Map<String, String> result = new HashMap<>();
        result.put("status", "UP");
        result.put("service", "Payment System");
        return result;
    }

    /**
     * 隐藏敏感信息
     */
    private String maskSensitiveInfo(String info) {
        if (info == null || info.length() <= 8) {
            return "****";
        }
        return info.substring(0, 4) + "****" + info.substring(info.length() - 4);
    }
}
