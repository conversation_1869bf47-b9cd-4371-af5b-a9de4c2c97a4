package com.example.paymentsystem.service;

import com.example.paymentsystem.dto.PaymentRequest;
import com.example.paymentsystem.dto.PaymentResponse;
import com.example.paymentsystem.entity.Order;

/**
 * 支付处理器接口
 */
public interface PaymentProcessor {
    
    /**
     * 获取支付方式代码
     */
    String getPaymentMethod();
    
    /**
     * 创建支付订单
     * @param order 订单信息
     * @param request 支付请求
     * @return 支付响应
     */
    PaymentResponse createPayment(Order order, PaymentRequest request);
    
    /**
     * 查询支付状态
     * @param orderId 订单ID
     * @return 支付响应
     */
    PaymentResponse queryPayment(String orderId);
    
    /**
     * 处理支付回调
     * @param callbackData 回调数据
     * @return 处理结果
     */
    boolean handleCallback(String callbackData);
    
    /**
     * 取消支付
     * @param orderId 订单ID
     * @return 是否成功
     */
    boolean cancelPayment(String orderId);
    
    /**
     * 退款
     * @param orderId 订单ID
     * @param refundAmount 退款金额
     * @param reason 退款原因
     * @return 是否成功
     */
    boolean refund(String orderId, java.math.BigDecimal refundAmount, String reason);
}
