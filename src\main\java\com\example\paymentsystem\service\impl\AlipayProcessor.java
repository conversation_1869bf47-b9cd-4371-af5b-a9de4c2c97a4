package com.example.paymentsystem.service.impl;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.domain.AlipayTradePagePayModel;
import com.alipay.api.request.AlipayTradePagePayRequest;
import com.alipay.api.response.AlipayTradePagePayResponse;
import com.example.paymentsystem.config.PaymentProperties;
import com.example.paymentsystem.dto.PaymentRequest;
import com.example.paymentsystem.dto.PaymentResponse;
import com.example.paymentsystem.entity.Order;
import com.example.paymentsystem.enums.PaymentMethod;
import com.example.paymentsystem.service.PaymentProcessor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 支付宝支付处理器
 */
@Service
public class AlipayProcessor implements PaymentProcessor {
    
    private static final Logger logger = LoggerFactory.getLogger(AlipayProcessor.class);
    
    @Autowired
    private AlipayClient alipayClient;
    
    @Autowired
    private PaymentProperties paymentProperties;
    
    @Override
    public String getPaymentMethod() {
        return PaymentMethod.ALIPAY.getCode();
    }
    
    @Override
    public PaymentResponse createPayment(Order order, PaymentRequest request) {
        try {
            // 创建支付宝支付请求
            AlipayTradePagePayRequest alipayRequest = new AlipayTradePagePayRequest();
            
            // 设置回调地址
            alipayRequest.setReturnUrl(request.getReturnUrl());
            alipayRequest.setNotifyUrl(paymentProperties.getCommon().getNotifyUrl());
            
            // 设置请求参数
            AlipayTradePagePayModel model = new AlipayTradePagePayModel();
            model.setOutTradeNo(order.getOrderId());
            model.setTotalAmount(order.getAmount().toString());
            model.setSubject(order.getSubject());
            model.setBody(order.getDescription());
            model.setProductCode("FAST_INSTANT_TRADE_PAY");
            
            alipayRequest.setBizModel(model);
            
            // 调用支付宝API
            AlipayTradePagePayResponse response = alipayClient.pageExecute(alipayRequest);
            
            if (response.isSuccess()) {
                logger.info("支付宝支付创建成功，订单号：{}", order.getOrderId());
                
                PaymentResponse paymentResponse = new PaymentResponse();
                paymentResponse.setOrderId(order.getOrderId());
                paymentResponse.setPaymentUrl(response.getBody());
                paymentResponse.setStatus("PENDING");
                paymentResponse.setMessage("支付创建成功");
                
                return paymentResponse;
            } else {
                logger.error("支付宝支付创建失败，订单号：{}，错误信息：{}", order.getOrderId(), response.getMsg());
                
                PaymentResponse paymentResponse = new PaymentResponse();
                paymentResponse.setOrderId(order.getOrderId());
                paymentResponse.setStatus("FAILED");
                paymentResponse.setMessage("支付创建失败：" + response.getMsg());
                
                return paymentResponse;
            }
            
        } catch (AlipayApiException e) {
            logger.error("支付宝支付创建异常，订单号：{}", order.getOrderId(), e);
            
            PaymentResponse paymentResponse = new PaymentResponse();
            paymentResponse.setOrderId(order.getOrderId());
            paymentResponse.setStatus("FAILED");
            paymentResponse.setMessage("支付创建异常：" + e.getMessage());
            
            return paymentResponse;
        }
    }
    
    @Override
    public PaymentResponse queryPayment(String orderId) {
        // TODO: 实现支付宝支付查询
        PaymentResponse response = new PaymentResponse();
        response.setOrderId(orderId);
        response.setStatus("PENDING");
        response.setMessage("查询功能待实现");
        return response;
    }
    
    @Override
    public boolean handleCallback(String callbackData) {
        // TODO: 实现支付宝回调处理
        logger.info("收到支付宝回调：{}", callbackData);
        return true;
    }
    
    @Override
    public boolean cancelPayment(String orderId) {
        // TODO: 实现支付宝支付取消
        logger.info("取消支付宝支付，订单号：{}", orderId);
        return true;
    }
    
    @Override
    public boolean refund(String orderId, BigDecimal refundAmount, String reason) {
        // TODO: 实现支付宝退款
        logger.info("支付宝退款，订单号：{}，退款金额：{}，原因：{}", orderId, refundAmount, reason);
        return true;
    }
}
