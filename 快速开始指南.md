# 通用支付系统快速开始指南

## 1. 环境准备

### 1.1 技术栈要求
- Java 8+
- Spring Boot 2.5+
- MySQL 5.7+
- Redis 3.0+
- Maven 3.6+

### 1.2 第三方账号准备
- 支付宝开放平台账号
- 微信商户平台账号
- 银行支付接口（可选）

## 2. 项目初始化

### 2.1 创建Spring Boot项目

```bash
# 使用Spring Initializr创建项目
curl https://start.spring.io/starter.zip \
  -d dependencies=web,data-jpa,mysql,redis \
  -d groupId=com.yourcompany \
  -d artifactId=payment-system \
  -d name=payment-system \
  -d packageName=com.yourcompany.payment \
  -o payment-system.zip

# 解压并进入项目目录
unzip payment-system.zip
cd payment-system
```

### 2.2 添加依赖

在 `pom.xml` 中添加支付相关依赖：

```xml
<dependencies>
    <!-- 支付宝SDK -->
    <dependency>
        <groupId>com.alipay.sdk</groupId>
        <artifactId>alipay-sdk-java</artifactId>
        <version>4.22.110.ALL</version>
    </dependency>
    
    <!-- 微信支付SDK -->
    <dependency>
        <groupId>com.github.wechatpay-apiv3</groupId>
        <artifactId>wechatpay-apache-httpclient</artifactId>
        <version>0.4.8</version>
    </dependency>
    
    <!-- JSON处理 -->
    <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>fastjson</artifactId>
        <version>1.2.83</version>
    </dependency>
    
    <!-- 工具类 -->
    <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-lang3</artifactId>
    </dependency>
</dependencies>
```

## 3. 配置文件设置

### 3.1 application.yml 配置

```yaml
server:
  port: 8080

spring:
  datasource:
    url: ******************************************************************************************
    username: root
    password: your_password
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0

# 支付配置
payment:
  alipay:
    app-id: "your-alipay-app-id"
    private-key: "your-private-key"
    public-key: "alipay-public-key"
    gateway-url: "https://openapi.alipay.com/gateway.do"
    
  wechat:
    app-id: "your-wechat-app-id"
    mch-id: "your-merchant-id"
    api-key: "your-api-key"
    cert-path: "/path/to/apiclient_cert.p12"
    
  common:
    timeout: 30000
    retry-times: 3
    notify-url: "https://your-domain.com/api/payment/notify"
```

## 4. 数据库初始化

### 4.1 创建数据库

```sql
CREATE DATABASE payment_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 4.2 创建表结构

```sql
-- 订单表
CREATE TABLE orders (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_id VARCHAR(64) UNIQUE NOT NULL,
    user_id BIGINT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'CNY',
    status VARCHAR(20) NOT NULL,
    payment_method VARCHAR(20),
    subject VARCHAR(256),
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_order_id (order_id),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status)
);

-- 支付记录表
CREATE TABLE payment_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_id VARCHAR(64) NOT NULL,
    transaction_id VARCHAR(128),
    payment_method VARCHAR(20) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) NOT NULL,
    third_party_response TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_order_id (order_id),
    INDEX idx_transaction_id (transaction_id)
);
```

## 5. 核心代码实现

### 5.1 复制核心文件

将以下文件复制到项目中：
- `PaymentGateway.java` - 支付网关
- `PaymentProcessor.java` - 支付处理器接口
- `AlipayProcessor.java` - 支付宝处理器
- `WechatProcessor.java` - 微信支付处理器
- `OrderService.java` - 订单服务
- `PaymentController.java` - 控制器

### 5.2 启动类配置

```java
@SpringBootApplication
@EnableJpaRepositories
@EnableConfigurationProperties(PaymentConfig.class)
public class PaymentSystemApplication {
    public static void main(String[] args) {
        SpringApplication.run(PaymentSystemApplication.class, args);
    }
}
```

## 6. 测试验证

### 6.1 启动应用

```bash
mvn spring-boot:run
```

### 6.2 测试支付创建

```bash
curl -X POST http://localhost:8080/api/payment/create \
  -H "Content-Type: application/json" \
  -d '{
    "userId": 12345,
    "amount": 0.01,
    "paymentMethod": "ALIPAY",
    "subject": "测试商品",
    "description": "这是一个测试订单"
  }'
```

### 6.3 查看响应

成功响应示例：
```json
{
  "success": true,
  "data": {
    "orderId": "ORDER_1640995200000",
    "paymentUrl": "https://openapi.alipay.com/gateway.do?...",
    "qrCode": null,
    "status": "PENDING"
  }
}
```

## 7. 部署上线

### 7.1 生产环境配置

```yaml
# application-prod.yml
spring:
  datasource:
    url: ************************************
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    
payment:
  alipay:
    app-id: ${ALIPAY_APP_ID}
    private-key: ${ALIPAY_PRIVATE_KEY}
    public-key: ${ALIPAY_PUBLIC_KEY}
    gateway-url: "https://openapi.alipay.com/gateway.do"
```

### 7.2 Docker部署

```dockerfile
FROM openjdk:8-jre-slim

COPY target/payment-system-1.0.0.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "/app.jar"]
```

### 7.3 构建和运行

```bash
# 构建项目
mvn clean package

# 构建Docker镜像
docker build -t payment-system:1.0.0 .

# 运行容器
docker run -d \
  --name payment-system \
  -p 8080:8080 \
  -e SPRING_PROFILES_ACTIVE=prod \
  -e DB_USERNAME=root \
  -e DB_PASSWORD=password \
  payment-system:1.0.0
```

## 8. 监控和维护

### 8.1 健康检查

```bash
# 检查应用状态
curl http://localhost:8080/actuator/health

# 检查支付接口
curl http://localhost:8080/api/payment/health
```

### 8.2 日志监控

```bash
# 查看应用日志
docker logs -f payment-system

# 查看支付相关日志
tail -f logs/payment.log
```

### 8.3 性能监控

- 使用Prometheus + Grafana监控系统性能
- 配置支付成功率、响应时间等关键指标
- 设置异常告警

## 9. 常见问题

### 9.1 支付宝配置问题

**问题**: 签名验证失败
**解决**: 检查私钥格式，确保使用正确的RSA2签名

### 9.2 微信支付问题

**问题**: 证书加载失败
**解决**: 确保证书路径正确，证书文件可读

### 9.3 回调处理问题

**问题**: 回调接口无法访问
**解决**: 确保回调URL可以从外网访问，使用HTTPS

## 10. 扩展功能

### 10.1 添加新的支付方式

1. 实现 `PaymentProcessor` 接口
2. 添加配置信息
3. 注册到支付路由器
4. 编写单元测试

### 10.2 支持多币种

1. 修改数据模型支持多币种
2. 添加汇率转换服务
3. 更新支付处理器

### 10.3 风控系统

1. 添加风控规则引擎
2. 实现实时风控检查
3. 支持黑名单管理

## 11. 技术支持

如有问题，请参考：
- 项目文档：`通用支付流程设计文档.md`
- 代码示例：`PaymentSystemExample.java`
- 官方文档：支付宝、微信支付官方文档

---

**注意事项**：
1. 生产环境请使用HTTPS
2. 定期更新SDK版本
3. 做好数据备份
4. 遵循PCI DSS安全标准
