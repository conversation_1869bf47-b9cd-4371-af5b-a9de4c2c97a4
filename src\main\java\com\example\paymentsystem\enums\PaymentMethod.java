package com.example.paymentsystem.enums;

/**
 * 支付方式枚举
 */
public enum PaymentMethod {
    ALIPAY("ALIPAY", "支付宝"),
    WECHAT("WECHAT", "微信支付"),
    BANK_CARD("BANK_CARD", "银行卡"),
    UNION_PAY("UNION_PAY", "银联支付");
    
    private final String code;
    private final String description;
    
    PaymentMethod(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    public static PaymentMethod fromCode(String code) {
        for (PaymentMethod method : values()) {
            if (method.code.equals(code)) {
                return method;
            }
        }
        throw new IllegalArgumentException("Unknown payment method code: " + code);
    }
}
