package com.example.paymentsystem.dto;

import java.math.BigDecimal;

/**
 * 支付请求DTO
 */
public class PaymentRequest {
    
    private Long userId;
    private BigDecimal amount;
    private String paymentMethod;
    private String subject;
    private String description;
    private String currency = "CNY";
    private String returnUrl;
    private String notifyUrl;
    
    // Constructors
    public PaymentRequest() {}
    
    public PaymentRequest(Long userId, BigDecimal amount, String paymentMethod, String subject, String description) {
        this.userId = userId;
        this.amount = amount;
        this.paymentMethod = paymentMethod;
        this.subject = subject;
        this.description = description;
    }
    
    // Getters and Setters
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public BigDecimal getAmount() {
        return amount;
    }
    
    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }
    
    public String getPaymentMethod() {
        return paymentMethod;
    }
    
    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }
    
    public String getSubject() {
        return subject;
    }
    
    public void setSubject(String subject) {
        this.subject = subject;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getCurrency() {
        return currency;
    }
    
    public void setCurrency(String currency) {
        this.currency = currency;
    }
    
    public String getReturnUrl() {
        return returnUrl;
    }
    
    public void setReturnUrl(String returnUrl) {
        this.returnUrl = returnUrl;
    }
    
    public String getNotifyUrl() {
        return notifyUrl;
    }
    
    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }
}
