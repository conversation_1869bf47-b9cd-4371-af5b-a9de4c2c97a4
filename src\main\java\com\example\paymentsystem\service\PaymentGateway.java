package com.example.paymentsystem.service;

import com.example.paymentsystem.dto.PaymentRequest;
import com.example.paymentsystem.dto.PaymentResponse;
import com.example.paymentsystem.entity.Order;
import com.example.paymentsystem.enums.OrderStatus;
import com.example.paymentsystem.enums.PaymentStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 支付网关服务
 */
@Service
public class PaymentGateway {
    
    private static final Logger logger = LoggerFactory.getLogger(PaymentGateway.class);
    
    @Autowired
    private OrderService orderService;
    
    @Autowired
    private List<PaymentProcessor> paymentProcessors;
    
    private Map<String, PaymentProcessor> processorMap = new HashMap<>();
    
    /**
     * 初始化支付处理器映射
     */
    @Autowired
    public void initProcessorMap() {
        for (PaymentProcessor processor : paymentProcessors) {
            processorMap.put(processor.getPaymentMethod(), processor);
        }
        logger.info("支付处理器初始化完成，支持的支付方式：{}", processorMap.keySet());
    }
    
    /**
     * 创建支付
     */
    public PaymentResponse createPayment(PaymentRequest request) {
        try {
            // 参数验证
            if (request.getUserId() == null || request.getAmount() == null || 
                request.getPaymentMethod() == null || request.getSubject() == null) {
                return createErrorResponse(null, "参数不完整");
            }
            
            // 获取支付处理器
            PaymentProcessor processor = processorMap.get(request.getPaymentMethod());
            if (processor == null) {
                return createErrorResponse(null, "不支持的支付方式：" + request.getPaymentMethod());
            }
            
            // 创建订单
            Order order = orderService.createOrder(request);
            
            // 调用支付处理器创建支付
            PaymentResponse response = processor.createPayment(order, request);
            
            // 更新订单状态
            if ("SUCCESS".equals(response.getStatus())) {
                orderService.updateOrderStatus(order.getOrderId(), OrderStatus.PROCESSING);
                orderService.updatePaymentRecordStatus(order.getOrderId(), request.getPaymentMethod(), 
                    PaymentStatus.PROCESSING, response.getTransactionId());
            } else if ("FAILED".equals(response.getStatus())) {
                orderService.updateOrderStatus(order.getOrderId(), OrderStatus.FAILED);
                orderService.updatePaymentRecordStatus(order.getOrderId(), request.getPaymentMethod(), 
                    PaymentStatus.FAILED, null);
            }
            
            return response;
            
        } catch (Exception e) {
            logger.error("创建支付异常", e);
            return createErrorResponse(null, "系统异常：" + e.getMessage());
        }
    }
    
    /**
     * 查询支付状态
     */
    public PaymentResponse queryPayment(String orderId, String paymentMethod) {
        try {
            PaymentProcessor processor = processorMap.get(paymentMethod);
            if (processor == null) {
                return createErrorResponse(orderId, "不支持的支付方式：" + paymentMethod);
            }
            
            return processor.queryPayment(orderId);
            
        } catch (Exception e) {
            logger.error("查询支付状态异常，订单号：{}", orderId, e);
            return createErrorResponse(orderId, "查询异常：" + e.getMessage());
        }
    }
    
    /**
     * 处理支付回调
     */
    public boolean handleCallback(String paymentMethod, String callbackData) {
        try {
            PaymentProcessor processor = processorMap.get(paymentMethod);
            if (processor == null) {
                logger.error("不支持的支付方式回调：{}", paymentMethod);
                return false;
            }
            
            return processor.handleCallback(callbackData);
            
        } catch (Exception e) {
            logger.error("处理支付回调异常，支付方式：{}", paymentMethod, e);
            return false;
        }
    }
    
    /**
     * 创建错误响应
     */
    private PaymentResponse createErrorResponse(String orderId, String message) {
        PaymentResponse response = new PaymentResponse();
        response.setOrderId(orderId);
        response.setStatus("FAILED");
        response.setMessage(message);
        return response;
    }
}
