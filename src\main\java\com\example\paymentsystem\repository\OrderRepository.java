package com.example.paymentsystem.repository;

import com.example.paymentsystem.entity.Order;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 订单数据访问接口
 */
@Repository
public interface OrderRepository extends JpaRepository<Order, Long> {
    
    /**
     * 根据订单ID查找订单
     */
    Optional<Order> findByOrderId(String orderId);
    
    /**
     * 根据用户ID查找订单列表
     */
    List<Order> findByUserId(Long userId);
    
    /**
     * 根据状态查找订单列表
     */
    List<Order> findByStatus(String status);
    
    /**
     * 根据用户ID和状态查找订单列表
     */
    List<Order> findByUserIdAndStatus(Long userId, String status);
    
    /**
     * 根据支付方式查找订单列表
     */
    List<Order> findByPaymentMethod(String paymentMethod);
}
