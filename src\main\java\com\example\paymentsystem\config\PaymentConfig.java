package com.example.paymentsystem.config;

import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 支付配置类
 */
@Configuration
public class PaymentConfig {
    
    @Autowired
    private PaymentProperties paymentProperties;
    
    /**
     * 支付宝客户端配置
     */
    @Bean
    public AlipayClient alipayClient() {
        return new DefaultAlipayClient(
            paymentProperties.getAlipay().getGatewayUrl(),
            paymentProperties.getAlipay().getAppId(),
            paymentProperties.getAlipay().getPrivateKey(),
            "json",
            "UTF-8",
            paymentProperties.getAlipay().getPublicKey(),
            "RSA2"
        );
    }
}
