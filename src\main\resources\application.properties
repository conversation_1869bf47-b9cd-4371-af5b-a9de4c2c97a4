# 服务器配置
server.port=8080

# 应用配置
spring.application.name=payment-system

# 数据库配置
spring.datasource.url=***********************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=your_password
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA配置
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.properties.hibernate.format_sql=true

# Redis配置
spring.data.redis.host=localhost
spring.data.redis.port=6379
spring.data.redis.password=
spring.data.redis.database=0
spring.data.redis.timeout=3000ms

# 支付宝配置
payment.alipay.app-id=your-alipay-app-id
payment.alipay.private-key=your-private-key
payment.alipay.public-key=alipay-public-key
payment.alipay.gateway-url=https://openapi.alipay.com/gateway.do

# 微信支付配置
payment.wechat.app-id=your-wechat-app-id
payment.wechat.mch-id=your-merchant-id
payment.wechat.api-key=your-api-key
payment.wechat.cert-path=/path/to/apiclient_cert.p12

# 通用支付配置
payment.common.timeout=30000
payment.common.retry-times=3
payment.common.notify-url=https://your-domain.com/api/payment/notify

# 日志配置
logging.level.com.example.paymentsystem=DEBUG
logging.level.org.springframework.web=INFO
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

# 开发环境配置
spring.profiles.active=dev

# JSON配置
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8

# 文件上传配置
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# 连接池配置
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000
