server:
  port: 8080

spring:
  application:
    name: payment-system
    
  datasource:
    url: ******************************************************************************************
    username: root
    password: your_password
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0

# 支付配置
payment:
  alipay:
    app-id: "your-alipay-app-id"
    private-key: "your-private-key"
    public-key: "alipay-public-key"
    gateway-url: "https://openapi.alipay.com/gateway.do"
    
  wechat:
    app-id: "your-wechat-app-id"
    mch-id: "your-merchant-id"
    api-key: "your-api-key"
    cert-path: "/path/to/apiclient_cert.p12"
    
  common:
    timeout: 30000
    retry-times: 3
    notify-url: "https://your-domain.com/api/payment/notify"
