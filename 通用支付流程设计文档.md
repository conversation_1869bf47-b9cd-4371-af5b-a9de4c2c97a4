# 通用支付流程设计文档

## 1. 概述

本文档描述了一个通用的支付流程系统设计，支持多种支付方式（支付宝、微信支付、银行卡等），具有良好的扩展性和可维护性。

## 2. 系统架构

### 2.1 整体架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   支付网关      │    │  第三方支付     │
│                 │    │                 │    │                 │
│ - 支付页面      │◄──►│ - 统一接口      │◄──►│ - 支付宝        │
│ - 订单管理      │    │ - 路由分发      │    │ - 微信支付      │
│ - 支付结果      │    │ - 参数转换      │    │ - 银行卡支付    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   业务系统      │    │   支付核心      │    │   通知系统      │
│                 │    │                 │    │                 │
│ - 订单系统      │◄──►│ - 支付处理      │◄──►│ - 异步通知      │
│ - 用户系统      │    │ - 状态管理      │    │ - 回调处理      │
│ - 商品系统      │    │ - 风控检查      │    │ - 重试机制      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 核心组件

1. **支付网关 (Payment Gateway)**
   - 统一支付接口
   - 支付方式路由
   - 参数标准化

2. **支付处理器 (Payment Processor)**
   - 具体支付逻辑
   - 第三方API调用
   - 结果处理

3. **订单管理 (Order Management)**
   - 订单创建与更新
   - 状态跟踪
   - 数据持久化

4. **通知系统 (Notification System)**
   - 异步回调处理
   - 重试机制
   - 状态同步

## 3. 支付流程

### 3.1 标准支付流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Frontend as 前端
    participant Gateway as 支付网关
    participant Processor as 支付处理器
    participant ThirdParty as 第三方支付
    participant Business as 业务系统

    User->>Frontend: 1. 发起支付请求
    Frontend->>Gateway: 2. 提交支付信息
    Gateway->>Business: 3. 创建订单
    Business-->>Gateway: 4. 返回订单信息
    Gateway->>Processor: 5. 选择支付处理器
    Processor->>ThirdParty: 6. 调用第三方API
    ThirdParty-->>Processor: 7. 返回支付凭证
    Processor-->>Gateway: 8. 返回支付结果
    Gateway-->>Frontend: 9. 返回支付页面/二维码
    Frontend-->>User: 10. 显示支付界面
    
    User->>ThirdParty: 11. 完成支付
    ThirdParty->>Gateway: 12. 异步通知
    Gateway->>Business: 13. 更新订单状态
    Gateway->>Frontend: 14. 通知前端
    Frontend->>User: 15. 显示支付结果
```

### 3.2 详细步骤说明

#### 步骤1-4: 订单创建
1. 用户选择商品，点击支付
2. 前端收集支付信息（金额、商品、支付方式等）
3. 支付网关验证参数，创建支付订单
4. 业务系统生成订单号，保存订单信息

#### 步骤5-9: 支付发起
5. 根据支付方式选择对应的处理器
6. 调用第三方支付API（如支付宝、微信）
7. 获取支付凭证（二维码、跳转链接等）
8. 返回支付结果给网关
9. 前端展示支付界面

#### 步骤10-15: 支付完成
10. 用户在第三方平台完成支付
11. 第三方平台异步通知支付结果
12. 更新订单状态
13. 通知前端支付结果
14. 用户看到支付成功页面

## 4. 代码结构设计

### 4.1 目录结构

```
payment-system/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/
│   │   │       └── payment/
│   │   │           ├── gateway/          # 支付网关
│   │   │           │   ├── PaymentGateway.java
│   │   │           │   └── PaymentRouter.java
│   │   │           ├── processor/        # 支付处理器
│   │   │           │   ├── PaymentProcessor.java
│   │   │           │   ├── AlipayProcessor.java
│   │   │           │   ├── WechatProcessor.java
│   │   │           │   └── BankCardProcessor.java
│   │   │           ├── model/           # 数据模型
│   │   │           │   ├── PaymentRequest.java
│   │   │           │   ├── PaymentResponse.java
│   │   │           │   ├── Order.java
│   │   │           │   └── PaymentResult.java
│   │   │           ├── service/         # 业务服务
│   │   │           │   ├── OrderService.java
│   │   │           │   ├── PaymentService.java
│   │   │           │   └── NotificationService.java
│   │   │           ├── controller/      # 控制器
│   │   │           │   ├── PaymentController.java
│   │   │           │   └── CallbackController.java
│   │   │           ├── config/          # 配置
│   │   │           │   ├── PaymentConfig.java
│   │   │           │   └── DatabaseConfig.java
│   │   │           └── util/            # 工具类
│   │   │               ├── SignatureUtil.java
│   │   │               ├── HttpUtil.java
│   │   │               └── JsonUtil.java
│   │   └── resources/
│   │       ├── application.yml
│   │       └── payment-config.yml
│   └── test/
└── docs/
    ├── api.md
    └── deployment.md
```

### 4.2 核心接口设计

#### PaymentProcessor 接口
```java
public interface PaymentProcessor {
    PaymentResult createPayment(PaymentRequest request);
    PaymentResult queryPayment(String orderId);
    PaymentResult refund(RefundRequest request);
    boolean verifyCallback(Map<String, String> params);
}
```

#### PaymentRequest 模型
```java
public class PaymentRequest {
    private String orderId;
    private BigDecimal amount;
    private String currency;
    private PaymentMethod method;
    private String subject;
    private String description;
    private String notifyUrl;
    private String returnUrl;
    private Map<String, Object> extraParams;
}
```

## 5. 关键技术点

### 5.1 安全性
- **签名验证**: 所有请求和回调都需要签名验证
- **HTTPS**: 强制使用HTTPS传输
- **参数加密**: 敏感参数加密传输
- **防重放**: 时间戳和随机数防重放攻击

### 5.2 可靠性
- **幂等性**: 支付请求支持幂等操作
- **重试机制**: 网络异常自动重试
- **超时处理**: 设置合理的超时时间
- **降级策略**: 第三方服务异常时的降级方案

### 5.3 监控与日志
- **全链路日志**: 记录完整的支付流程
- **性能监控**: 监控接口响应时间
- **异常告警**: 异常情况及时告警
- **数据统计**: 支付成功率、失败原因统计

## 6. 数据库设计

### 6.1 订单表 (orders)
```sql
CREATE TABLE orders (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_id VARCHAR(64) UNIQUE NOT NULL,
    user_id BIGINT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'CNY',
    status VARCHAR(20) NOT NULL,
    payment_method VARCHAR(20),
    subject VARCHAR(256),
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 6.2 支付记录表 (payment_records)
```sql
CREATE TABLE payment_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_id VARCHAR(64) NOT NULL,
    transaction_id VARCHAR(128),
    payment_method VARCHAR(20) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) NOT NULL,
    third_party_response TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 7. 配置管理

### 7.1 支付配置示例
```yaml
payment:
  alipay:
    app-id: "your-app-id"
    private-key: "your-private-key"
    public-key: "alipay-public-key"
    gateway-url: "https://openapi.alipay.com/gateway.do"
    
  wechat:
    app-id: "your-app-id"
    mch-id: "your-mch-id"
    api-key: "your-api-key"
    cert-path: "/path/to/cert.p12"
    
  common:
    timeout: 30000
    retry-times: 3
    notify-url: "https://your-domain.com/payment/notify"
```

## 8. 部署与运维

### 8.1 部署架构
- **负载均衡**: 多实例部署，负载均衡
- **数据库**: 主从复制，读写分离
- **缓存**: Redis缓存热点数据
- **消息队列**: 异步处理支付通知

### 8.2 监控指标
- 支付成功率
- 平均响应时间
- 异常率
- 第三方接口可用性

## 9. 扩展性设计

### 9.1 新增支付方式
1. 实现 PaymentProcessor 接口
2. 添加配置信息
3. 注册到支付路由器
4. 编写单元测试

### 9.2 国际化支持
- 多币种支持
- 本地化支付方式
- 汇率转换
- 合规性要求

## 10. 测试策略

### 10.1 单元测试
- 支付处理器测试
- 业务逻辑测试
- 工具类测试

### 10.2 集成测试
- 第三方API集成测试
- 数据库操作测试
- 端到端流程测试

### 10.3 压力测试
- 并发支付测试
- 大量订单处理测试
- 系统稳定性测试

## 11. 核心代码实现示例

### 11.1 支付网关核心类

```java
@Service
public class PaymentGateway {

    @Autowired
    private PaymentRouter paymentRouter;

    @Autowired
    private OrderService orderService;

    @Autowired
    private NotificationService notificationService;

    /**
     * 创建支付
     */
    public PaymentResponse createPayment(PaymentRequest request) {
        try {
            // 1. 参数验证
            validatePaymentRequest(request);

            // 2. 创建订单
            Order order = orderService.createOrder(request);

            // 3. 选择支付处理器
            PaymentProcessor processor = paymentRouter.getProcessor(request.getMethod());

            // 4. 调用支付处理器
            PaymentResult result = processor.createPayment(request);

            // 5. 更新订单状态
            orderService.updateOrderStatus(order.getOrderId(), result.getStatus());

            // 6. 构建响应
            return buildPaymentResponse(result);

        } catch (Exception e) {
            log.error("创建支付失败", e);
            throw new PaymentException("支付创建失败", e);
        }
    }

    /**
     * 处理支付回调
     */
    public void handleCallback(PaymentMethod method, Map<String, String> params) {
        try {
            // 1. 获取处理器
            PaymentProcessor processor = paymentRouter.getProcessor(method);

            // 2. 验证回调签名
            if (!processor.verifyCallback(params)) {
                throw new PaymentException("回调签名验证失败");
            }

            // 3. 解析回调数据
            String orderId = params.get("out_trade_no");
            String status = params.get("trade_status");

            // 4. 更新订单状态
            orderService.updateOrderStatus(orderId, status);

            // 5. 发送通知
            notificationService.sendPaymentNotification(orderId, status);

        } catch (Exception e) {
            log.error("处理支付回调失败", e);
            throw new PaymentException("回调处理失败", e);
        }
    }
}
```

### 11.2 支付宝处理器实现

```java
@Component
public class AlipayProcessor implements PaymentProcessor {

    @Autowired
    private AlipayConfig alipayConfig;

    @Override
    public PaymentResult createPayment(PaymentRequest request) {
        try {
            // 构建支付宝请求参数
            AlipayTradePagePayRequest alipayRequest = new AlipayTradePagePayRequest();
            alipayRequest.setReturnUrl(request.getReturnUrl());
            alipayRequest.setNotifyUrl(request.getNotifyUrl());

            // 设置业务参数
            JSONObject bizContent = new JSONObject();
            bizContent.put("out_trade_no", request.getOrderId());
            bizContent.put("total_amount", request.getAmount());
            bizContent.put("subject", request.getSubject());
            bizContent.put("product_code", "FAST_INSTANT_TRADE_PAY");

            alipayRequest.setBizContent(bizContent.toString());

            // 调用支付宝API
            AlipayClient alipayClient = new DefaultAlipayClient(
                alipayConfig.getGatewayUrl(),
                alipayConfig.getAppId(),
                alipayConfig.getPrivateKey(),
                "json",
                "UTF-8",
                alipayConfig.getPublicKey(),
                "RSA2"
            );

            AlipayTradePagePayResponse response = alipayClient.pageExecute(alipayRequest);

            // 构建返回结果
            PaymentResult result = new PaymentResult();
            result.setSuccess(response.isSuccess());
            result.setPaymentUrl(response.getBody());
            result.setOrderId(request.getOrderId());

            return result;

        } catch (Exception e) {
            log.error("支付宝支付创建失败", e);
            throw new PaymentException("支付宝支付失败", e);
        }
    }

    @Override
    public boolean verifyCallback(Map<String, String> params) {
        try {
            return AlipaySignature.rsaCheckV1(
                params,
                alipayConfig.getPublicKey(),
                "UTF-8",
                "RSA2"
            );
        } catch (Exception e) {
            log.error("支付宝回调验签失败", e);
            return false;
        }
    }
}
```

### 11.3 订单服务实现

```java
@Service
@Transactional
public class OrderService {

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private PaymentRecordRepository paymentRecordRepository;

    /**
     * 创建订单
     */
    public Order createOrder(PaymentRequest request) {
        // 检查订单是否已存在
        Order existingOrder = orderRepository.findByOrderId(request.getOrderId());
        if (existingOrder != null) {
            if (existingOrder.getStatus() == OrderStatus.PAID) {
                throw new PaymentException("订单已支付");
            }
            return existingOrder;
        }

        // 创建新订单
        Order order = new Order();
        order.setOrderId(request.getOrderId());
        order.setUserId(request.getUserId());
        order.setAmount(request.getAmount());
        order.setCurrency(request.getCurrency());
        order.setSubject(request.getSubject());
        order.setDescription(request.getDescription());
        order.setStatus(OrderStatus.PENDING);
        order.setPaymentMethod(request.getMethod());
        order.setCreatedAt(new Date());

        return orderRepository.save(order);
    }

    /**
     * 更新订单状态
     */
    public void updateOrderStatus(String orderId, String paymentStatus) {
        Order order = orderRepository.findByOrderId(orderId);
        if (order == null) {
            throw new PaymentException("订单不存在: " + orderId);
        }

        // 转换支付状态为订单状态
        OrderStatus orderStatus = convertPaymentStatusToOrderStatus(paymentStatus);

        // 防止重复更新
        if (order.getStatus() == orderStatus) {
            return;
        }

        order.setStatus(orderStatus);
        order.setUpdatedAt(new Date());
        orderRepository.save(order);

        // 记录支付记录
        recordPaymentHistory(order, paymentStatus);
    }

    private void recordPaymentHistory(Order order, String paymentStatus) {
        PaymentRecord record = new PaymentRecord();
        record.setOrderId(order.getOrderId());
        record.setPaymentMethod(order.getPaymentMethod());
        record.setAmount(order.getAmount());
        record.setStatus(paymentStatus);
        record.setCreatedAt(new Date());

        paymentRecordRepository.save(record);
    }
}
```

## 12. 异常处理和错误码

### 12.1 异常类设计

```java
public class PaymentException extends RuntimeException {
    private String errorCode;
    private String errorMessage;

    public PaymentException(String errorCode, String errorMessage) {
        super(errorMessage);
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    public PaymentException(String errorMessage, Throwable cause) {
        super(errorMessage, cause);
        this.errorMessage = errorMessage;
    }
}
```

### 12.2 错误码定义

```java
public enum PaymentErrorCode {
    INVALID_PARAMETER("P001", "参数错误"),
    ORDER_NOT_FOUND("P002", "订单不存在"),
    ORDER_ALREADY_PAID("P003", "订单已支付"),
    PAYMENT_TIMEOUT("P004", "支付超时"),
    SIGNATURE_ERROR("P005", "签名验证失败"),
    THIRD_PARTY_ERROR("P006", "第三方支付异常"),
    NETWORK_ERROR("P007", "网络异常"),
    SYSTEM_ERROR("P999", "系统异常");

    private final String code;
    private final String message;

    PaymentErrorCode(String code, String message) {
        this.code = code;
        this.message = message;
    }
}
```

## 13. 配置和环境管理

### 13.1 多环境配置

```yaml
# application-dev.yml (开发环境)
payment:
  alipay:
    gateway-url: "https://openapi.alipaydev.com/gateway.do"
    app-id: "dev-app-id"
  wechat:
    api-url: "https://api.mch.weixin.qq.com"
  common:
    timeout: 10000

# application-prod.yml (生产环境)
payment:
  alipay:
    gateway-url: "https://openapi.alipay.com/gateway.do"
    app-id: "prod-app-id"
  wechat:
    api-url: "https://api.mch.weixin.qq.com"
  common:
    timeout: 30000
```

### 13.2 配置类

```java
@ConfigurationProperties(prefix = "payment")
@Data
public class PaymentConfig {
    private AlipayConfig alipay;
    private WechatConfig wechat;
    private CommonConfig common;

    @Data
    public static class AlipayConfig {
        private String appId;
        private String privateKey;
        private String publicKey;
        private String gatewayUrl;
    }

    @Data
    public static class WechatConfig {
        private String appId;
        private String mchId;
        private String apiKey;
        private String certPath;
        private String apiUrl;
    }

    @Data
    public static class CommonConfig {
        private int timeout;
        private int retryTimes;
        private String notifyUrl;
    }
}
```
