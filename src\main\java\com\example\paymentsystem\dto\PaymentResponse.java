package com.example.paymentsystem.dto;

/**
 * 支付响应DTO
 */
public class PaymentResponse {
    
    private String orderId;
    private String paymentUrl;
    private String qrCode;
    private String status;
    private String message;
    private String transactionId;
    
    // Constructors
    public PaymentResponse() {}
    
    public PaymentResponse(String orderId, String status) {
        this.orderId = orderId;
        this.status = status;
    }
    
    public PaymentResponse(String orderId, String paymentUrl, String status) {
        this.orderId = orderId;
        this.paymentUrl = paymentUrl;
        this.status = status;
    }
    
    // Getters and Setters
    public String getOrderId() {
        return orderId;
    }
    
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }
    
    public String getPaymentUrl() {
        return paymentUrl;
    }
    
    public void setPaymentUrl(String paymentUrl) {
        this.paymentUrl = paymentUrl;
    }
    
    public String getQrCode() {
        return qrCode;
    }
    
    public void setQrCode(String qrCode) {
        this.qrCode = qrCode;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public String getTransactionId() {
        return transactionId;
    }
    
    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }
}
