package com.example.paymentsystem.repository;

import com.example.paymentsystem.entity.PaymentRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 支付记录数据访问接口
 */
@Repository
public interface PaymentRecordRepository extends JpaRepository<PaymentRecord, Long> {
    
    /**
     * 根据订单ID查找支付记录列表
     */
    List<PaymentRecord> findByOrderId(String orderId);
    
    /**
     * 根据第三方交易ID查找支付记录
     */
    Optional<PaymentRecord> findByTransactionId(String transactionId);
    
    /**
     * 根据订单ID和支付方式查找支付记录
     */
    Optional<PaymentRecord> findByOrderIdAndPaymentMethod(String orderId, String paymentMethod);
    
    /**
     * 根据状态查找支付记录列表
     */
    List<PaymentRecord> findByStatus(String status);
    
    /**
     * 根据支付方式查找支付记录列表
     */
    List<PaymentRecord> findByPaymentMethod(String paymentMethod);
}
